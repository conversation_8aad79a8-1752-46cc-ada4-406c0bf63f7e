{"name": "My workflow", "nodes": [{"parameters": {"command": "cd /data/gits/Hotel && \\\ngit branch -a --sort=-committerdate --format='%(refname:short)' | grep -v HEAD | sed 's/origin\\///' | uniq"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-380, -540], "id": "c3e7f714-54d2-46c9-8971-940d171ae554", "name": "获取分支列表", "executeOnce": true}, {"parameters": {"command": "cd /data/gits/ && \\\necho \"开始Git操作...\" && \\\nif [ -d \"Hotel/.git\" ]; then \\\n  echo \"仓库已存在，执行git pull...\" && \\\n  cd Hotel && \\\n  git pull --progress https://cnb:<EMAIL>/twomiles/wudongdelvdian/Hotel.git 2>&1; \\\nelse \\\n  echo \"仓库不存在，执行git clone...\" && \\\n  git clone --progress https://cnb:<EMAIL>/twomiles/wudongdelvdian/Hotel.git 2>&1; \\\nfi && \\\necho \"Git操作完成！\""}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-600, -540], "id": "75d036c1-7a8b-4d0b-b923-f36c69ef60af", "name": "检查仓库", "alwaysOutputData": true, "executeOnce": true}, {"parameters": {"formTitle": "运行工作流", "formDescription": "点击确定按钮 ↓ 开始", "options": {"buttonLabel": "确定"}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-860, -540], "id": "cac003cc-a957-4a6b-91da-ae3554e44d46", "name": "开始运行", "webhookId": "a793acca-38bb-4bad-bdec-3a24c088ba04"}, {"parameters": {"defineForm": "json", "jsonOutput": "=[\n  {\n\t\t\"fieldLabel\": \"分支列表\",\n\t\t\"fieldType\": \"dropdown\",\n\t\t\"fieldOptions\": {\n\t\t\t\"values\": {{ $json.stdout.split(\"\\n\").map(branch => ({ option: branch })) }}\n\t\t},\n\t\t\"requiredField\": true\n\t}\n]", "options": {"formTitle": "选择本次要操作的分支", "buttonLabel": "确定"}}, "type": "n8n-nodes-base.form", "typeVersion": 1, "position": [-160, -540], "id": "783ce865-5026-4600-a9e4-b3def80b098d", "name": "选择分支", "webhookId": "6bc101e7-6d9f-4a36-911d-fb1104419be0", "alwaysOutputData": true, "retryOnFail": true, "maxTries": 5}], "connections": {"检查仓库": {"main": [[{"node": "获取分支列表", "type": "main", "index": 0}]]}, "获取分支列表": {"main": [[{"node": "选择分支", "type": "main", "index": 0}]]}, "开始运行": {"main": [[{"node": "检查仓库", "type": "main", "index": 0}]]}}}